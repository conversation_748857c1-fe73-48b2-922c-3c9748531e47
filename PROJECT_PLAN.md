# Vertcoin Mining Project Plan - M1 Mac (8GB RAM)

## Executive Summary
Vertcoin (VTC) mining on M1 Mac using CPU mining with the Verthash algorithm. Expected low profitability but good for learning blockchain concepts.

## Hardware Assessment
- **CPU**: Apple M1 (8-core, 4 performance + 4 efficiency)
- **RAM**: 8GB unified memory
- **Expected Hashrate**: 50-150 H/s
- **Power Consumption**: ~10-15W additional
- **Thermal Management**: Monitor temps, use cooling pad if needed

## Software Requirements

### Core Mining Software
- **Vertcoin Core Wallet** (official)
- **VerthashMiner** (CPU miner for Verthash algorithm)
- **Homebrew** (package manager)

### Monitoring Tools
- **Activity Monitor** (built-in)
- **TG Pro** or **Macs Fan Control** (temperature monitoring)

## Installation Steps

### Phase 1: Environment Setup
1. Install Homebrew:
   ```bash
   /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
   ```

2. Install dependencies:
   ```bash
   brew install cmake boost openssl
   ```

### Phase 2: Wallet Setup
1. Download Vertcoin Core from official website
2. Install and sync blockchain (requires ~8GB storage)
3. Create new wallet and backup seed phrase
4. Generate receiving address

### Phase 3: Miner Installation
1. Clone VerthashMiner:
   ```bash
   git clone https://github.com/CryptoGraphics/VerthashMiner.git
   cd VerthashMiner
   ```

2. Build for M1:
   ```bash
   mkdir build && cd build
   cmake ..
   make -j4
   ```

3. Download Verthash data file (~1.2GB):
   ```bash
   ./VerthashMiner --data-file verthash.dat --verify
   ```

## Mining Configuration

### Pool Selection (Recommended)
- **Primary**: p2pool-vtc.org (decentralized)
- **Backup**: zergpool.com
- **Alternative**: Mining Pool Hub

### Mining Script
```bash
#!/bin/bash
./VerthashMiner \
  --algorithm verthash \
  --pool stratum+tcp://p2pool-vtc.org:9181 \
  --user YOUR_VTC_ADDRESS \
  --threads 6 \
  --data-file verthash.dat
```

### Optimization Settings
- **Threads**: Start with 6 (leave 2 cores for system)
- **Intensity**: Auto-adjust based on temperature
- **RAM Allocation**: 2-3GB for Verthash data

## Performance Expectations

### Hashrate Targets
- **Conservative**: 50-80 H/s
- **Optimized**: 80-120 H/s
- **Maximum**: 120-150 H/s

### Revenue Projections (Current Market)
- **Daily Earnings**: $0.05-0.15
- **Monthly Earnings**: $1.50-4.50
- **Electricity Cost**: ~$0.10-0.20/day
- **Net Profit**: Minimal to negative

## Risk Management

### Thermal Protection
- Monitor CPU temps (keep under 85°C)
- Reduce threads if temps exceed 80°C
- Use cooling pad for extended mining

### Hardware Safety
- Enable automatic thermal throttling
- Set CPU usage limits in Activity Monitor
- Regular system health checks

### Security Measures
- Use official software only
- Verify wallet backups
- Enable 2FA where available
- Monitor pool reputation

## Timeline & Milestones

### Week 1: Setup
- Environment preparation
- Software installation
- Initial configuration

### Week 2: Optimization
- Performance tuning
- Thermal management
- Pool testing

### Week 3-4: Monitoring
- Stability testing
- Revenue tracking
- System health monitoring

## Budget & Resources

### Initial Costs
- Software: Free
- Electricity: ~$3-6/month
- Cooling accessories: $20-50 (optional)

### Ongoing Expenses
- Electricity: $0.10-0.20/day
- Pool fees: 1-2%
- Hardware wear: Minimal

## Success Metrics

### Technical KPIs
- Uptime: >95%
- Average hashrate: >80 H/s
- CPU temperature: <80°C
- System stability: No crashes

### Financial KPIs
- Break-even point: Not expected
- Learning value: High
- Experience gained: Significant

## Exit Strategy

### When to Stop Mining
- If daily losses exceed $0.50
- CPU temperatures consistently >85°C
- System instability issues
- Better opportunities arise

### Asset Recovery
- Transfer VTC to exchange or hold
- Repurpose mining setup for other projects
- Document lessons learned

## Troubleshooting Guide

### Common Issues
- **Low hashrate**: Adjust thread count
- **High temps**: Reduce intensity
- **Pool connection**: Check firewall/internet
- **Wallet sync**: Verify blockchain data

### Emergency Procedures
- Kill mining process: `killall VerthashMiner`
- System cooling: Reduce all processes
- Backup recovery: Restore from seed phrase

## Conclusion
This project serves primarily as educational experience. Financial returns are minimal, but provides hands-on blockchain and mining knowledge. Monitor closely for thermal issues and adjust accordingly.

## Next Steps
1. Review hardware specifications
2. Download required software
3. Set up development environment
4. Begin installation process
5. Start with conservative settings